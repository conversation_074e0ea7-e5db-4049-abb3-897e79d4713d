import { useState, useEffect, useRef } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import { ResponsiveContainer, RadarChart, PolarGrid, PolarAngleAxis, PolarRadiusAxis, Radar, Tooltip, BarChart, Bar, XAxis, YAxis, CartesianGrid } from 'recharts';
import apiService from '../../services/apiService';
import EnhancedLoadingScreen from '../UI/EnhancedLoadingScreen';
import useScrollToTop from '../../hooks/useScrollToTop';

const ResultViaIs = () => {
  const { resultId } = useParams();
  const navigate = useNavigate();
  const [result, setResult] = useState(null);
  const [error, setError] = useState('');
  const fetchInProgressRef = useRef(false);
  const abortControllerRef = useRef(null);

  // Scroll to top when component mounts or route changes
  useScrollToTop();

  useEffect(() => {
    // Prevent duplicate calls
    if (fetchInProgressRef.current) {
      return;
    }

    const fetchResult = async (retryCount = 0) => {
      const maxRetries = 5;
      const retryDelay = Math.min(1000 * Math.pow(2, retryCount), 10000); // Exponential backoff, max 10s

      // Create new AbortController for this fetch sequence
      abortControllerRef.current = new AbortController();

      try {
        fetchInProgressRef.current = true;
        const response = await apiService.getResultById(resultId);

        // Check if component is still mounted and request wasn't aborted
        if (!abortControllerRef.current?.signal.aborted) {
          if (response.success) {
            setResult(response.data);
            fetchInProgressRef.current = false;
          }
        }
      } catch (err) {
        // Check if the error is due to abort
        if (abortControllerRef.current?.signal.aborted) {
          return;
        }

        // If it's a 404 and we haven't exceeded max retries, try again
        if (err.response?.status === 404 && retryCount < maxRetries) {
          setTimeout(() => {
            // Check if component is still mounted before retrying
            if (!abortControllerRef.current?.signal.aborted) {
              fetchResult(retryCount + 1);
            }
          }, retryDelay);
        } else {
          // Final error after all retries or non-404 error
          const errorMessage = retryCount >= maxRetries
            ? `Result not found after ${maxRetries + 1} attempts. The analysis may still be processing.`
            : err.response?.data?.message || 'Failed to load results';
          setError(errorMessage);
          fetchInProgressRef.current = false;
        }
      }
    };

    if (resultId) {
      fetchResult();
    } else {
      navigate('/dashboard');
    }

    // Cleanup function
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
      fetchInProgressRef.current = false;
    };
  }, [resultId, navigate]);

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-GB', {
      day: 'numeric',
      month: 'short',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // VIA Character Strengths organized by virtue categories
  const viaCategories = {
    wisdom: {
      name: 'Wisdom & Knowledge',
      icon: '◆',
      color: '#374151',
      strengths: ['creativity', 'curiosity', 'judgment', 'love_of_learning', 'perspective']
    },
    courage: {
      name: 'Courage',
      icon: '◇',
      color: '#374151',
      strengths: ['bravery', 'perseverance', 'honesty', 'zest']
    },
    humanity: {
      name: 'Humanity',
      icon: '◈',
      color: '#374151',
      strengths: ['love', 'kindness', 'social_intelligence']
    },
    justice: {
      name: 'Justice',
      icon: '◉',
      color: '#374151',
      strengths: ['teamwork', 'fairness', 'leadership']
    },
    temperance: {
      name: 'Temperance',
      icon: '◎',
      color: '#374151',
      strengths: ['forgiveness', 'humility', 'prudence', 'self_regulation']
    },
    transcendence: {
      name: 'Transcendence',
      icon: '◐',
      color: '#374151',
      strengths: ['appreciation_of_beauty', 'gratitude', 'hope', 'humor', 'spirituality']
    }
  };

  const strengthLabels = {
    creativity: 'Creativity',
    curiosity: 'Curiosity',
    judgment: 'Critical Thinking',
    love_of_learning: 'Love of Learning',
    perspective: 'Perspective',
    bravery: 'Bravery',
    perseverance: 'Perseverance',
    honesty: 'Honesty',
    zest: 'Zest',
    love: 'Love',
    kindness: 'Kindness',
    social_intelligence: 'Social Intelligence',
    teamwork: 'Teamwork',
    fairness: 'Fairness',
    leadership: 'Leadership',
    forgiveness: 'Forgiveness',
    humility: 'Humility',
    prudence: 'Prudence',
    self_regulation: 'Self-Regulation',
    appreciation_of_beauty: 'Appreciation of Beauty',
    gratitude: 'Gratitude',
    hope: 'Hope',
    humor: 'Humor',
    spirituality: 'Spirituality'
  };

  // VIA Character Strengths detailed data structure
  const viaStrengthsData = {
    'creativity': {
      name: 'Creativity',
      category: 'wisdom',
      description: 'Thinking of novel and productive ways to conceptualize and do things',
      highTraits: [
        'Memiliki kemampuan untuk menghasilkan ide-ide baru dan inovatif',
        'Mampu melihat masalah dari perspektif yang berbeda dan unik',
        'Senang bereksperimen dengan pendekatan baru dalam menyelesaikan tugas',
        'Memiliki imajinasi yang kuat dan kemampuan berpikir di luar kotak',
        'Dapat mengombinasikan ide-ide yang berbeda menjadi solusi kreatif'
      ],
      lowTraits: [
        'Lebih menyukai pendekatan yang sudah terbukti dan konvensional',
        'Merasa lebih nyaman mengikuti prosedur yang telah ditetapkan',
        'Kurang tertarik untuk mencoba metode atau ide yang belum teruji',
        'Lebih fokus pada efisiensi daripada inovasi',
        'Mengutamakan stabilitas dan prediktabilitas dalam pendekatan'
      ],
      implications: {
        high: 'Sangat cocok untuk peran yang membutuhkan inovasi dan pemikiran kreatif, seperti desain, penelitian dan pengembangan, atau bidang seni dan media.',
        low: 'Lebih sesuai untuk peran yang membutuhkan konsistensi dan keandalan, seperti operasional, administrasi, atau implementasi sistem yang sudah ada.'
      }
    },
    'curiosity': {
      name: 'Curiosity',
      category: 'wisdom',
      description: 'Taking an interest in ongoing experience for its own sake',
      highTraits: [
        'Memiliki rasa ingin tahu yang tinggi terhadap berbagai topik dan pengalaman',
        'Senang mengeksplorasi ide-ide baru dan mempelajari hal-hal yang belum diketahui',
        'Aktif mencari informasi dan pengetahuan dari berbagai sumber',
        'Terbuka terhadap pengalaman baru dan tantangan intelektual',
        'Memiliki motivasi intrinsik untuk terus belajar dan berkembang'
      ],
      lowTraits: [
        'Lebih fokus pada area keahlian yang sudah dikuasai',
        'Merasa cukup dengan pengetahuan yang sudah dimiliki',
        'Kurang tertarik untuk mengeksplorasi topik di luar bidang utama',
        'Lebih menyukai rutinitas dan hal-hal yang sudah familiar',
        'Mengutamakan pendalaman daripada perluasan pengetahuan'
      ],
      implications: {
        high: 'Ideal untuk peran yang melibatkan penelitian, pembelajaran berkelanjutan, atau eksplorasi bidang baru seperti R&D, jurnalisme, atau konsultasi.',
        low: 'Lebih cocok untuk peran yang membutuhkan keahlian mendalam di bidang spesifik dan konsistensi dalam penerapan pengetahuan yang sudah ada.'
      }
    },
    'judgment': {
      name: 'Critical Thinking',
      category: 'wisdom',
      description: 'Thinking things through and examining them from all sides',
      highTraits: [
        'Mampu menganalisis informasi secara objektif dan menyeluruh',
        'Memiliki kemampuan untuk mengevaluasi argumen dan bukti dengan kritis',
        'Dapat mengidentifikasi bias dan kelemahan dalam pemikiran',
        'Senang mempertimbangkan berbagai perspektif sebelum mengambil keputusan',
        'Memiliki standar tinggi untuk kualitas pemikiran dan reasoning'
      ],
      lowTraits: [
        'Lebih mengandalkan intuisi dan perasaan dalam pengambilan keputusan',
        'Cenderung menerima informasi tanpa analisis mendalam',
        'Kurang tertarik pada detail dan nuansa dalam argumen',
        'Lebih menyukai keputusan yang cepat daripada analisis yang panjang',
        'Mengutamakan harmoni daripada kritik konstruktif'
      ],
      implications: {
        high: 'Sangat sesuai untuk peran yang membutuhkan analisis mendalam dan pengambilan keputusan strategis, seperti manajemen senior, audit, atau konsultasi strategis.',
        low: 'Lebih cocok untuk peran yang membutuhkan empati dan hubungan interpersonal yang kuat, atau pekerjaan yang bersifat eksekutif dengan panduan yang jelas.'
      }
    },
    'love_of_learning': {
      name: 'Love of Learning',
      category: 'wisdom',
      description: 'Mastering new skills, topics, and bodies of knowledge',
      highTraits: [
        'Memiliki motivasi tinggi untuk terus mengembangkan pengetahuan dan keterampilan',
        'Senang mengikuti pelatihan, kursus, atau program pengembangan diri',
        'Aktif mencari peluang untuk mempelajari hal-hal baru di bidang pekerjaan',
        'Memiliki kemampuan untuk belajar secara mandiri dan berkelanjutan',
        'Merasa energik dan termotivasi ketika menghadapi tantangan pembelajaran baru'
      ],
      lowTraits: [
        'Lebih fokus pada penerapan pengetahuan yang sudah dimiliki',
        'Merasa cukup dengan tingkat keahlian saat ini',
        'Kurang tertarik pada program pengembangan atau pelatihan tambahan',
        'Lebih menyukai stabilitas dalam rutinitas kerja',
        'Mengutamakan efisiensi dalam tugas yang sudah dikuasai'
      ],
      implications: {
        high: 'Ideal untuk lingkungan kerja yang dinamis dan berkembang pesat, seperti teknologi, akademik, atau bidang yang membutuhkan adaptasi berkelanjutan.',
        low: 'Lebih sesuai untuk peran yang membutuhkan keahlian stabil dan konsisten, seperti operasional rutin atau spesialisasi teknis yang sudah mapan.'
      }
    },
    'perspective': {
      name: 'Perspective',
      category: 'wisdom',
      description: 'Being able to provide wise counsel; having ways of looking at the world',
      highTraits: [
        'Mampu memberikan nasihat yang bijaksana dan perspektif yang luas',
        'Memiliki kemampuan untuk melihat gambaran besar dan konteks yang lebih luas',
        'Dapat membantu orang lain memahami situasi dari berbagai sudut pandang',
        'Memiliki pengalaman hidup yang kaya dan dapat mengambil pelajaran darinya',
        'Dipercaya sebagai sumber kebijaksaan dan panduan oleh orang lain'
      ],
      lowTraits: [
        'Lebih fokus pada detail dan aspek teknis daripada gambaran besar',
        'Kurang nyaman memberikan nasihat atau panduan kepada orang lain',
        'Lebih menyukai peran eksekutif daripada advisory',
        'Mengutamakan tindakan praktis daripada refleksi filosofis',
        'Lebih tertarik pada solusi konkret daripada pemahaman konseptual'
      ],
      implications: {
        high: 'Sangat cocok untuk peran kepemimpinan, mentoring, atau konsultasi yang membutuhkan kebijaksaan dan pandangan strategis jangka panjang.',
        low: 'Lebih sesuai untuk peran teknis atau operasional yang membutuhkan fokus pada detail dan implementasi praktis.'
      }
    },
    'bravery': {
      name: 'Bravery',
      category: 'courage',
      description: 'Not shrinking from threat, challenge, difficulty, or pain',
      highTraits: [
        'Berani menghadapi tantangan dan situasi yang sulit atau berisiko',
        'Mampu mengambil keputusan sulit meskipun ada tekanan atau ketidakpastian',
        'Tidak mudah mundur ketika menghadapi hambatan atau kritik',
        'Bersedia membela prinsip dan nilai-nilai meskipun tidak populer',
        'Memiliki ketahanan mental yang kuat dalam menghadapi adversitas'
      ],
      lowTraits: [
        'Lebih menyukai lingkungan kerja yang stabil dan dapat diprediksi',
        'Cenderung menghindari konflik atau situasi yang kontroversial',
        'Lebih nyaman dengan tugas-tugas yang sudah familiar dan aman',
        'Mengutamakan konsensus dan harmoni daripada mengambil posisi yang berani',
        'Lebih suka bekerja dalam tim daripada mengambil tanggung jawab individual yang besar'
      ],
      implications: {
        high: 'Ideal untuk peran kepemimpinan dalam situasi krisis, change management, atau bidang yang membutuhkan pengambilan risiko strategis.',
        low: 'Lebih cocok untuk peran yang membutuhkan stabilitas, kerja sama tim, dan lingkungan kerja yang harmonis dan mendukung.'
      }
    }
  };

  const getStrengthDescription = (strength) => {
    return viaStrengthsData[strength]?.description || 'A valuable character strength';
  };

  // VIA explanation data
  const viaExplanation = {
    title: "VIA Character Strengths Survey",
    description: "VIA (Values in Action) Survey mengidentifikasi kekuatan karakter yang merupakan inti dari kepribadian positif. Survey ini mengukur 24 kekuatan karakter yang diorganisir ke dalam 6 kategori kebajikan universal.",
    developer: "Dikembangkan oleh Dr. Christopher Peterson dan Dr. Martin Seligman (2004) berdasarkan penelitian lintas budaya",
    validity: "Telah divalidasi secara ilmiah di lebih dari 190 negara dengan jutaan responden. Instrumen ini digunakan secara luas dalam psikologi positif dan pengembangan karakter.",
    purpose: "Mengidentifikasi kekuatan karakter signature Anda yang dapat digunakan untuk meningkatkan kesejahteraan, kinerja, dan kepuasan hidup.",
    dimensions: Object.entries(viaCategories).map(([key, category]) => ({
      key,
      name: category.name,
      description: `${category.strengths.length} kekuatan karakter`,
      icon: category.icon
    }))
  };

  const getViaIsInsights = (viaIsData) => {
    if (!viaIsData) return { top: [], bottom: [], byCategory: {} };

    const entries = Object.entries(viaIsData).sort(([,a], [,b]) => b - a);

    // Group by categories
    const byCategory = {};
    Object.entries(viaCategories).forEach(([categoryKey, category]) => {
      byCategory[categoryKey] = entries
        .filter(([strength]) => category.strengths.includes(strength))
        .map(([strength, score]) => ({ strength, score, label: strengthLabels[strength] }));
    });

    return {
      top: entries.slice(0, 5),
      bottom: entries.slice(-5),
      byCategory
    };
  };

  const getScoreLevel = (score) => {
    if (score >= 4.5) return { level: 'Signature Strength', intensity: 'text-green-700 font-semibold', bg: 'bg-green-50' };
    if (score >= 3.5) return { level: 'High Strength', intensity: 'text-blue-700 font-medium', bg: 'bg-blue-50' };
    if (score >= 2.5) return { level: 'Moderate Strength', intensity: 'text-gray-700 font-medium', bg: 'bg-gray-50' };
    if (score >= 1.5) return { level: 'Lower Strength', intensity: 'text-orange-700 font-medium', bg: 'bg-orange-50' };
    return { level: 'Least Strength', intensity: 'text-red-700 font-medium', bg: 'bg-red-50' };
  };

  // Prepare radar chart data for virtue categories
  const prepareRadarData = (viaIsData) => {
    if (!viaIsData) return [];

    const categoryScores = {};
    Object.entries(viaCategories).forEach(([categoryKey, category]) => {
      const categoryStrengths = category.strengths.filter(strength => viaIsData[strength] !== undefined);
      if (categoryStrengths.length > 0) {
        const avgScore = categoryStrengths.reduce((sum, strength) => sum + (viaIsData[strength] || 0), 0) / categoryStrengths.length;
        categoryScores[categoryKey] = avgScore;
      }
    });

    return Object.entries(categoryScores).map(([key, score]) => ({
      category: viaCategories[key].name,
      value: score,
      fullValue: score
    }));
  };

  // Prepare bar chart data for all strengths
  const prepareBarData = (viaIsData) => {
    if (!viaIsData) return [];

    return Object.entries(viaIsData)
      .map(([key, value]) => ({
        name: strengthLabels[key] || key.replace(/_/g, ' '),
        value: value || 0,
        key: key,
        category: Object.entries(viaCategories).find(([_, cat]) =>
          cat.strengths.includes(key)
        )?.[0] || 'other'
      }))
      .sort((a, b) => b.value - a.value);
  };

  // Custom tooltip for radar chart
  const CustomRadarTooltip = ({ active, payload, label }) => {
    if (active && payload && payload.length) {
      const data = payload[0];
      const categoryKey = Object.keys(viaCategories).find(key => viaCategories[key].name === label);
      const category = viaCategories[categoryKey];

      return (
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          className="bg-white p-4 rounded shadow-lg border border-gray-200 max-w-sm"
        >
          <div className="flex items-center mb-2">
            <span className="text-xl mr-2 text-gray-700">{category?.icon}</span>
            <h4 className="font-semibold text-gray-900">{label}</h4>
          </div>
          <div className="text-lg font-bold mb-2 text-gray-900">
            Average Score: {data.payload.fullValue.toFixed(2)}
          </div>
          <div className="text-xs text-gray-500">
            Includes: {category?.strengths.map(s => strengthLabels[s]).join(', ')}
          </div>
        </motion.div>
      );
    }
    return null;
  };

  // Custom tooltip for bar chart
  const CustomBarTooltip = ({ active, payload, label }) => {
    if (active && payload && payload.length) {
      const data = payload[0];
      const categoryKey = data.payload.category;
      const category = viaCategories[categoryKey];
      const scoreLevel = getScoreLevel(data.value);

      return (
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          className="bg-white p-4 rounded shadow-lg border border-gray-200 max-w-sm"
        >
          <h4 className="font-semibold text-gray-900 mb-2">{label}</h4>
          <div className="flex items-center gap-2 mb-2">
            <div className="text-lg font-bold text-gray-900">
              Score: {data.value.toFixed(2)}
            </div>
            <span className={`text-sm font-medium ${scoreLevel.intensity}`}>
              ({scoreLevel.level})
            </span>
          </div>
          <div className="text-xs text-gray-500 mb-2">
            <strong>Category:</strong> {category?.name} <span className="text-gray-700">{category?.icon}</span>
          </div>
          <p className="text-xs text-gray-600">
            {getStrengthDescription(data.payload.key)}
          </p>
        </motion.div>
      );
    }
    return null;
  };

  // Navigation cards data
  const navigationCards = [
    {
      title: 'Trait Kepribadian',
      subtitle: 'OCEAN Assessment',
      description: 'Pahami dimensi kepribadian utama Anda.',
      path: `/results/${resultId}/ocean`,
      color: 'from-blue-500 to-blue-600'
    },
    {
      title: 'Minat Karier',
      subtitle: 'RIASEC Assessment',
      description: 'Jelajahi minat karier dan preferensi lingkungan kerja Anda.',
      path: `/results/${resultId}/riasec`,
      color: 'from-emerald-500 to-emerald-600'
    },
    {
      title: 'Persona Karier',
      subtitle: 'Integrated Profile',
      description: 'Rekomendasi karier komprehensif berdasarkan profil Anda.',
      path: `/results/${resultId}/persona`,
      color: 'from-indigo-500 to-indigo-600'
    }
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Loading State */}
        {!result && !error && (
          <EnhancedLoadingScreen
            title="Loading VIA Character Strengths..."
            subtitle="Analyzing your character strengths profile"
            skeletonCount={4}
            className="min-h-[600px]"
          />
        )}

        {/* Error State */}
        {error && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="bg-white border border-gray-200 rounded p-6 shadow-sm"
          >
            <div className="flex items-center">
              <div className="text-gray-400 mr-3">⚠️</div>
              <div>
                <h3 className="text-gray-900 font-semibold">Unable to Load Results</h3>
                <p className="text-gray-600 text-sm mt-1">{error}</p>
                <div className="mt-4 space-x-3">
                  <button
                    onClick={() => window.location.reload()}
                    className="bg-gray-900 text-white px-4 py-2 rounded text-sm hover:bg-gray-800 transition-colors"
                  >
                    Retry
                  </button>
                  <button
                    onClick={() => navigate(`/results/${resultId}`)}
                    className="bg-gray-100 text-gray-700 px-4 py-2 rounded text-sm hover:bg-gray-200 transition-colors"
                  >
                    Back to Overview
                  </button>
                </div>
              </div>
            </div>
          </motion.div>
        )}

        {/* Main Content */}
        {result && (
          <>
            {/* Header */}
            <motion.div
              initial={{ opacity: 0, y: -20 }}
              animate={{ opacity: 1, y: 0 }}
              className="mb-8"
            >
              <div className="flex justify-between items-start mb-6">
                <div>
                  <h1 className="text-3xl font-bold text-gray-900 mb-2">
                    VIA Character Strengths Assessment
                  </h1>
                  <p className="text-gray-600 max-w-2xl">
                    Discover your signature character strengths based on the VIA (Values in Action) Survey.
                    This assessment identifies your top character strengths that represent your authentic self and core virtues.
                  </p>
                </div>
                <div className="flex space-x-3">
                  <button
                    onClick={() => navigate(`/results/${resultId}`)}
                    className="px-4 py-2 border border-gray-300 text-gray-700 rounded hover:bg-gray-50 transition-colors"
                  >
                    ← Back
                  </button>
                  <button
                    onClick={() => navigate('/dashboard')}
                    className="px-4 py-2 bg-gray-900 text-white rounded hover:bg-gray-800 transition-colors"
                  >
                    Dashboard
                  </button>
                </div>
              </div>

              <div className="bg-white rounded p-4 border border-gray-200 shadow-sm">
                <div className="flex items-center justify-between text-sm text-gray-600">
                  <div className="flex items-center">
                    <span className="w-2 h-2 bg-gray-900 rounded-sm mr-2"></span>
                    Completed: {formatDate(result.created_at)}
                  </div>
                  <span className="bg-gray-100 text-gray-700 px-3 py-1 rounded text-xs font-medium">
                    VIA Character Strengths
                  </span>
                </div>
              </div>
            </motion.div>

            {/* VIA Explanation Section */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.2 }}
              className="mb-8"
            >
              <div className="bg-white rounded border border-gray-200 shadow-sm overflow-hidden">
                <div className="bg-gradient-to-r from-gray-50 to-slate-50 border-b border-gray-200 p-6">
                  <div className="flex items-center mb-4">
                    <div className="w-12 h-12 bg-gray-900 rounded flex items-center justify-center mr-4">
                      <span className="text-white text-xl font-bold">◆</span>
                    </div>
                    <div>
                      <h2 className="text-2xl font-bold text-gray-900">{viaExplanation.title}</h2>
                      <p className="text-gray-700 font-medium">Character Strengths & Virtues</p>
                    </div>
                  </div>
                  <p className="text-gray-700 leading-relaxed mb-4">{viaExplanation.description}</p>

                  <div className="grid md:grid-cols-2 gap-4 mb-4">
                    <div className="bg-white p-4 rounded border border-gray-200">
                      <h4 className="font-semibold text-gray-900 mb-2 flex items-center">
                        <span className="w-6 h-6 bg-gray-900 rounded-sm flex items-center justify-center mr-2">
                          <span className="text-white text-xs">◇</span>
                        </span>
                        Pengembang
                      </h4>
                      <p className="text-sm text-gray-700">{viaExplanation.developer}</p>
                    </div>
                    <div className="bg-white p-4 rounded border border-gray-200">
                      <h4 className="font-semibold text-gray-900 mb-2 flex items-center">
                        <span className="w-6 h-6 bg-gray-900 rounded-sm flex items-center justify-center mr-2">
                          <span className="text-white text-xs">◈</span>
                        </span>
                        Validitas Ilmiah
                      </h4>
                      <p className="text-sm text-gray-700">{viaExplanation.validity}</p>
                    </div>
                  </div>

                  <div className="bg-white p-4 rounded border border-gray-200">
                    <h4 className="font-semibold text-gray-900 mb-2 flex items-center">
                      <span className="w-6 h-6 bg-gray-900 rounded-sm flex items-center justify-center mr-2">
                        <span className="text-white text-xs">◉</span>
                      </span>
                      Tujuan Assessment
                    </h4>
                    <p className="text-sm text-gray-700">{viaExplanation.purpose}</p>
                  </div>
                </div>
              </div>
            </motion.div>

            {/* Virtue Categories Radar Chart */}
            {result.assessment_data?.viaIs && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.4 }}
                className="mb-8"
              >
                <div className="bg-white rounded border border-gray-200 shadow-sm p-6">
                  <div className="text-center mb-6">
                    <h3 className="text-xl font-bold text-gray-900 mb-2">Virtue Categories Profile</h3>
                    <p className="text-gray-600">Your average scores across the six virtue categories</p>
                  </div>

                  <div className="h-96">
                    <ResponsiveContainer width="100%" height="100%">
                      <RadarChart data={prepareRadarData(result.assessment_data.viaIs)}>
                        <PolarGrid stroke="#e5e7eb" />
                        <PolarAngleAxis
                          dataKey="category"
                          tick={{ fontSize: 12, fill: '#374151' }}
                        />
                        <PolarRadiusAxis
                          angle={90}
                          domain={[0, 5]}
                          tick={{ fontSize: 10, fill: '#6b7280' }}
                          tickCount={6}
                        />
                        <Radar
                          name="Virtue Score"
                          dataKey="value"
                          stroke="#374151"
                          fill="#374151"
                          fillOpacity={0.1}
                          strokeWidth={2}
                        />
                        <Tooltip content={<CustomRadarTooltip />} />
                      </RadarChart>
                    </ResponsiveContainer>
                  </div>

                  <div className="mt-6 grid grid-cols-2 md:grid-cols-3 gap-3">
                    {Object.entries(viaCategories).map(([key, category]) => {
                      const categoryData = prepareRadarData(result.assessment_data.viaIs).find(
                        item => item.category === category.name
                      );
                      const scoreLevel = getScoreLevel(categoryData ? categoryData.fullValue : 0);
                      return (
                        <div key={key} className="text-center p-3 bg-gray-50 rounded">
                          <div className="text-xl mb-1 text-gray-700">{category.icon}</div>
                          <div className="text-lg font-bold mb-1 text-gray-900">
                            {categoryData ? categoryData.fullValue.toFixed(2) : 'N/A'}
                          </div>
                          <div className="text-xs text-gray-600 font-medium mb-1">{category.name}</div>
                          <div className={`text-xs ${scoreLevel.intensity}`}>
                            {scoreLevel.level}
                          </div>
                        </div>
                      );
                    })}
                  </div>
                </div>
              </motion.div>
            )}

            {/* All Strengths Bar Chart */}
            {result.assessment_data?.viaIs && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.6 }}
                className="mb-8"
              >
                <div className="bg-white rounded border border-gray-200 shadow-sm p-6">
                  <div className="text-center mb-6">
                    <h3 className="text-xl font-bold text-gray-900 mb-2">All 24 Character Strengths</h3>
                    <p className="text-gray-600">Complete ranking of your character strengths from highest to lowest</p>
                  </div>

                  <div className="h-96 overflow-hidden">
                    <ResponsiveContainer width="100%" height="100%">
                      <BarChart
                        data={prepareBarData(result.assessment_data.viaIs)}
                        margin={{ top: 20, right: 30, left: 20, bottom: 100 }}
                      >
                        <CartesianGrid strokeDasharray="3 3" stroke="#e5e7eb" />
                        <XAxis
                          dataKey="name"
                          tick={{ fontSize: 9, fill: '#374151' }}
                          angle={-45}
                          textAnchor="end"
                          height={100}
                          interval={0}
                        />
                        <YAxis
                          domain={[0, 5]}
                          tick={{ fontSize: 10, fill: '#6b7280' }}
                        />
                        <Tooltip content={<CustomBarTooltip />} />
                        <Bar
                          dataKey="value"
                          fill="#374151"
                          radius={[2, 2, 0, 0]}
                        />
                      </BarChart>
                    </ResponsiveContainer>
                  </div>
                </div>
              </motion.div>
            )}

            {/* Virtue Categories Detailed Analysis */}
            {result.assessment_data?.viaIs && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.8 }}
                className="mb-8"
              >
                <div className="bg-white rounded border border-gray-200 shadow-sm p-6">
                  <div className="text-center mb-6">
                    <h3 className="text-xl font-bold text-gray-900 mb-2">Virtue Categories Analysis</h3>
                    <p className="text-gray-600">Detailed breakdown of your character strengths by virtue categories</p>
                  </div>

                  {/* Virtue Categories Cards - 2 columns 3 rows layout */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {Object.entries(viaCategories).map(([categoryKey, category], index) => {
                      const categoryData = prepareRadarData(result.assessment_data.viaIs).find(
                        item => item.category === category.name
                      );
                      const categoryScore = categoryData ? categoryData.fullValue : 0;
                      const scoreLevel = getScoreLevel(categoryScore);
                      const isHigh = categoryScore >= 3.5;

                      return (
                        <motion.div
                          key={categoryKey}
                          initial={{ opacity: 0, y: 20 }}
                          animate={{ opacity: 1, y: 0 }}
                          transition={{ delay: index * 0.1 }}
                          className="bg-white rounded border border-gray-200 overflow-hidden shadow-sm hover:shadow-md transition-shadow"
                        >
                          {/* Header */}
                          <div className="bg-gray-50 border-b border-gray-200 p-6">
                            <div className="flex items-center justify-between">
                              <div className="flex items-center">
                                <span className="text-2xl mr-3 text-gray-700">{category.icon}</span>
                                <div>
                                  <h3 className="text-xl font-bold text-gray-900">{category.name}</h3>
                                  <p className="text-sm text-gray-600">{category.strengths.length} Character Strengths</p>
                                </div>
                              </div>
                              <div className="text-right">
                                <div className="text-2xl font-bold text-gray-900">{categoryScore.toFixed(2)}</div>
                                <div className={`text-sm ${scoreLevel.intensity}`}>{scoreLevel.level}</div>
                              </div>
                            </div>
                          </div>

                          {/* Content */}
                          <div className="p-6">
                            {/* Progress Bar */}
                            <div className="mb-6">
                              <div className="bg-gray-200 rounded-sm h-2">
                                <motion.div
                                  className="bg-gray-900 h-2 rounded-sm"
                                  initial={{ width: 0 }}
                                  animate={{ width: `${Math.min((categoryScore / 5) * 100, 100)}%` }}
                                  transition={{ duration: 1, delay: 0.5 + index * 0.1 }}
                                />
                              </div>
                            </div>

                            {/* Strengths in this category */}
                            <div className="mb-6">
                              <h4 className="font-semibold text-gray-900 mb-3">Strengths in this Category:</h4>
                              <div className="space-y-2">
                                {category.strengths.map((strength, idx) => {
                                  const strengthScore = result.assessment_data.viaIs[strength] || 0;
                                  const strengthLabel = strengthLabels[strength] || strength.replace(/_/g, ' ');
                                  return (
                                    <div key={idx} className="flex items-center justify-between text-sm">
                                      <span className="text-gray-700">{strengthLabel}</span>
                                      <span className="font-medium text-gray-900">{strengthScore.toFixed(2)}</span>
                                    </div>
                                  );
                                })}
                              </div>
                            </div>

                            {/* Category Implications */}
                            <div className="bg-gray-50 rounded p-4 border border-gray-100">
                              <h4 className="font-semibold text-gray-900 mb-2">Category Overview</h4>
                              <p className="text-sm text-gray-700">
                                {isHigh
                                  ? `Your high score in ${category.name} indicates strong development in these character strengths. This suggests you naturally embody the virtues associated with this category and can leverage these strengths in your personal and professional life.`
                                  : `Your moderate score in ${category.name} suggests room for growth in these character strengths. Consider focusing on developing these areas to enhance your overall character profile and personal effectiveness.`
                                }
                              </p>
                            </div>
                          </div>
                        </motion.div>
                      );
                    })}
                  </div>
                </div>
              </motion.div>
            )}

            {/* Main Grid Layout - 2 Columns */}
            {result.assessment_data?.viaIs && (
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
                {/* Left Column - Signature Strengths */}
                <motion.div
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.5, delay: 0.8 }}
                  className="space-y-6"
                >
                  <div className="bg-white rounded border border-gray-200 shadow-sm p-6">
                    <div className="flex items-center mb-6">
                      <div className="w-8 h-8 bg-gray-900 rounded flex items-center justify-center mr-3">
                        <span className="text-white text-sm font-bold">◆</span>
                      </div>
                      <h3 className="text-xl font-bold text-gray-900">Top 5 Signature Strengths</h3>
                    </div>

                    <p className="text-gray-600 mb-6 text-sm">
                      These are your strongest character traits that represent your most authentic self.
                      Focus on leveraging these strengths in your daily life and career.
                    </p>

                    <div className="space-y-4">
                      {getViaIsInsights(result.assessment_data.viaIs).top.map(([strength, score], idx) => {
                        const scoreLevel = getScoreLevel(score);
                        return (
                          <motion.div
                            key={strength}
                            initial={{ opacity: 0, y: 20 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ duration: 0.4, delay: 0.9 + idx * 0.1 }}
                            className={`p-4 rounded border ${scoreLevel.bg} border-gray-200`}
                          >
                            <div className="flex justify-between items-start mb-2">
                              <h4 className="font-semibold text-gray-900">
                                {strengthLabels[strength] || strength.replace(/_/g, ' ')}
                              </h4>
                              <span className={`text-sm px-2 py-1 rounded ${scoreLevel.intensity} bg-white`}>
                                {score.toFixed(2)}
                              </span>
                            </div>
                            <p className="text-sm text-gray-700 mb-3">
                              {getStrengthDescription(strength)}
                            </p>
                            <div className="bg-gray-200 rounded-full h-2">
                              <motion.div
                                className="bg-gray-900 h-2 rounded-full"
                                initial={{ width: 0 }}
                                animate={{ width: `${(score / 5) * 100}%` }}
                                transition={{ duration: 0.8, delay: 1.0 + idx * 0.1 }}
                              />
                            </div>
                          </motion.div>
                        );
                      })}
                    </div>
                  </div>
                </motion.div>

                {/* Right Column - Development Areas */}
                <motion.div
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.5, delay: 0.8 }}
                  className="space-y-6"
                >
                  <div className="bg-white rounded border border-gray-200 shadow-sm p-6">
                    <div className="flex items-center mb-6">
                      <div className="w-8 h-8 bg-orange-100 rounded flex items-center justify-center mr-3">
                        <svg className="w-5 h-5 text-orange-600" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                        </svg>
                      </div>
                      <h3 className="text-xl font-bold text-gray-900">Development Opportunities</h3>
                    </div>

                    <p className="text-gray-600 mb-6 text-sm">
                      These strengths scored lower but can be developed when they align with your goals.
                      Consider how strengthening these areas might benefit your personal growth.
                    </p>

                    <div className="space-y-4">
                      {getViaIsInsights(result.assessment_data.viaIs).bottom.map(([strength, score], idx) => {
                        const scoreLevel = getScoreLevel(score);
                        return (
                          <motion.div
                            key={strength}
                            initial={{ opacity: 0, y: 20 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ duration: 0.4, delay: 0.9 + idx * 0.1 }}
                            className={`p-4 rounded border ${scoreLevel.bg} border-gray-200`}
                          >
                            <div className="flex justify-between items-start mb-2">
                              <h4 className="font-semibold text-gray-900">
                                {strengthLabels[strength] || strength.replace(/_/g, ' ')}
                              </h4>
                              <span className={`text-sm px-2 py-1 rounded ${scoreLevel.intensity} bg-white`}>
                                {score.toFixed(2)}
                              </span>
                            </div>
                            <p className="text-sm text-gray-700 mb-3">
                              {getStrengthDescription(strength)}
                            </p>
                            <div className="bg-gray-200 rounded-full h-2">
                              <motion.div
                                className="bg-gray-600 h-2 rounded-full"
                                initial={{ width: 0 }}
                                animate={{ width: `${(score / 5) * 100}%` }}
                                transition={{ duration: 0.8, delay: 1.0 + idx * 0.1 }}
                              />
                            </div>
                          </motion.div>
                        );
                      })}
                    </div>
                  </div>
                </motion.div>
              </div>
            )}

            {/* Navigation to Other Results */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 1.4 }}
              className="mb-12"
            >
              <div className="bg-gradient-to-r from-gray-50 to-slate-50 rounded p-8 mb-8">
                <div className="text-center">
                  <div className="inline-flex items-center justify-center w-16 h-16 bg-gray-900 rounded mb-4">
                    <span className="text-white text-2xl font-bold">◎</span>
                  </div>
                  <h2 className="text-3xl font-bold text-gray-900 mb-3">
                    Jelajahi Profil Lengkap Anda
                  </h2>
                  <p className="text-gray-600 text-lg max-w-2xl mx-auto leading-relaxed">
                    Lanjutkan perjalanan Anda dengan mengeksplorasi aspek lain dari hasil assessment.
                    Setiap assessment memberikan wawasan unik tentang berbagai sisi kepribadian dan potensi karier Anda.
                  </p>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 max-w-6xl mx-auto">
                {navigationCards.map((card, index) => (
                  <motion.div
                    key={card.title}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.3, delay: 1.5 + index * 0.1 }}
                    whileHover={{
                      y: -4,
                      transition: { duration: 0.15 }
                    }}
                    className="group cursor-pointer"
                    onClick={() => navigate(card.path)}
                  >
                    <div className="bg-white rounded p-6 shadow-sm border border-gray-100 hover:shadow-xl hover:border-gray-200 transition-all duration-300 h-full">
                      <div className="flex flex-col h-full">
                        <div className="flex items-start justify-end mb-4">
                          <motion.svg
                            className="w-5 h-5 text-gray-400 group-hover:text-gray-600 transition-colors"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                            whileHover={{ x: 3 }}
                            transition={{ duration: 0.2 }}
                          >
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                          </motion.svg>
                        </div>

                        <div className="flex-grow">
                          <h3 className="text-xl font-bold text-gray-900 mb-2 group-hover:text-gray-700 transition-colors">
                            {card.title}
                          </h3>
                          <p className="text-sm text-gray-500 mb-3 font-semibold uppercase tracking-wide">
                            {card.subtitle}
                          </p>
                          <p className="text-gray-600 leading-relaxed">
                            {card.description}
                          </p>
                        </div>

                        <div className="mt-4 pt-4 border-t border-gray-100">
                          <div className="flex items-center text-sm font-medium text-gray-500 group-hover:text-blue-600 transition-colors">
                            <span>Lihat Assessment</span>
                            <motion.svg
                              className="w-4 h-4 ml-1"
                              fill="none"
                              stroke="currentColor"
                              viewBox="0 0 24 24"
                              whileHover={{ x: 2 }}
                              transition={{ duration: 0.2 }}
                            >
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8l4 4m0 0l-4 4m4-4H3" />
                            </motion.svg>
                          </div>
                        </div>
                      </div>
                    </div>
                  </motion.div>
                ))}
              </div>
            </motion.div>

          </>
        )}
      </div>
    </div>
  );
};

export default ResultViaIs;